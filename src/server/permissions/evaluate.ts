import {
  BASE_ROLE_PERMISSIONS,
  hasAllPermissions,
  sanitizeOverrides,
  type ResourceActions,
} from "@/libs/auth-permissions";
import { Roles } from "@/types/organization.types";
import { getRolePermissionMapFromBA } from "./role-source";

/**
 * Computes the effective permission map for a member within an organization.
 * - Built-in roles: use the static base role maps.
 * - Dynamic roles: loaded from Better Auth's organization role store.
 * Returns a map of resource -> allowed actions.
 */
export async function evaluateEffectivePermissionsServer(params: {
  role: string | null | undefined;
  organizationId: string;
}): Promise<ResourceActions> {
  const { role, organizationId } = params;
  if (!role) return {};

  // Built-ins are resolved locally for speed and determinism.
  if (role === Roles.ADMIN || role === Roles.MANAGER || role === Roles.MEMBER) {
    return BASE_ROLE_PERMISSIONS[role] ?? {};
  }

  // Otherwise, load from Better Auth (dynamic roles), then sanitize.
  const fromBA = await getRolePermissionMapFromBA({
    organizationId,
    roleName: role,
  });

  // Sanitize is idempotent but ensures the map matches our vocabulary.
  return sanitizeOverrides(fromBA);
}

/**
 * Utility to check if an effective permission map satisfies a capability spec.
 * This re-exports the predicate from the shared library for convenience.
 */
export { hasAllPermissions };
