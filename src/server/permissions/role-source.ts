import { auth } from "@/libs/auth";
import {
  sanitizeOverrides,
  type ResourceActions,
} from "@/libs/auth-permissions";
import { headers } from "next/headers";

/**
 * Fetch a role's permission map from Better Auth for a given organization.
 * The returned map is sanitized against our vocabulary to avoid drift.
 */
export async function getRolePermissionMapFromBA(options: {
  organizationId: string;
  roleName: string;
}): Promise<ResourceActions> {
  const { organizationId, roleName } = options;

  // Single call path: endpoint is available; sanitize and fail-closed on error.
  try {
    const role = await auth.api.getOrgRole({
      query: { organizationId, roleName },
      headers: await headers(),
    });
    const permission = role.permission;
    return sanitizeOverrides(permission);
  } catch (error) {
    console.error("Failed to fetch role from Better Auth:", error);
    return {};
  }
}
