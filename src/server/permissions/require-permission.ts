import { type CapabilitySpec } from "@/libs/auth-permissions";
import { db } from "@/server/db";
import { TRPCError } from "@trpc/server";
import {
  evaluateEffectivePermissionsServer,
  hasAllPermissions,
} from "./evaluate";

// Minimal context typing (avoid circular import) – relies on runtime shape provided in trpc context
type Ctx = {
  session: { user: { id: string } } | null;
  db: typeof db;
};

interface RequirePermissionArgs {
  ctx: Ctx;
  organizationId: string;
  spec: CapabilitySpec; // e.g. { project: ["publish"] }
}

// Computes missing actions for better error messaging.
function computeMissing(
  effective: Record<string, string[]>,
  spec: CapabilitySpec,
) {
  const missing: string[] = [];
  for (const [resource, actions] of Object.entries(spec)) {
    const allowed = new Set(effective[resource] || []);
    for (const action of actions) {
      if (!allowed.has(action)) missing.push(`${resource}.${action}`);
    }
  }
  return missing;
}

export async function requirePermission({
  ctx,
  organizationId,
  spec,
}: RequirePermissionArgs) {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  // Fetch membership so we know the caller's role in this org
  const membership = await ctx.db.member.findFirst({
    where: {
      userId: ctx.session.user.id,
      organizationId,
      deletedAt: null,
    },
    select: { role: true },
  });

  if (!membership) {
    // Hide existence details – align with NOT_FOUND pattern for unauthorized org access
    throw new TRPCError({ code: "NOT_FOUND", message: "Resource not found" });
  }

  const effective = await evaluateEffectivePermissionsServer({
    role: membership.role,
    organizationId,
  });

  if (!hasAllPermissions(effective, spec)) {
    const missing = computeMissing(effective, spec);
    // Provide a deterministic first missing permission in message
    throw new TRPCError({
      code: "FORBIDDEN",
      message: `Missing permission: ${missing[0]}`,
    });
  }

  return true;
}

export default requirePermission;
