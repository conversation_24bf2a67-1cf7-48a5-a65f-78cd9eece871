import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import {
  buildEffectivePermissions,
  capability,
  hasAllPermissions,
  legacyFlagsFromEffective,
  type CapabilitySpec,
} from "@/libs/auth-permissions";
import {
  useOrganizationBySlug,
  useOrganizationMemberRole,
} from "@/queries/organization.queries";
import { useMemo } from "react";

/**
 * usePermissions
 * ---------------------------------------------------------------------------
 * Central client‑side permission resolution & querying hook.
 *
 * Responsibilities:
 * 1. Reads the current organization (via slug) and member role.
 * 2. Builds an "effective" permission map by merging:
 *      base role capabilities  ∪  organization metadata overrides (additive only v1).
 * 3. Exposes a flexible `can()` helper for runtime checks.
 * 4. Provides `capability()` helper to construct specs programmatically.
 * 5. Supplies backward‑compatible boolean flags (existing UI still relies on these).
 *
 * Return Shape (partial):
 *   {
 *     role: "admin" | "manager" | "member" | undefined,
 *     effective: { [resource: string]: string[] },
 *     can: (resource, ...actions) => boolean OR (specObject) => boolean,
 *     capability: (resource, ...actions) => CapabilitySpec,
 *     // legacy flags:
 *     canPublishProject: boolean,
 *     canEditProject: boolean,
 *     ...
 *   }
 *
 * Usage Examples:
 * ---------------------------------------------------------------------------
 *   const perms = usePermissions();
 *   if (perms.can("project", "publish")) {
 *     // show Publish button
 *   }
 *
 *   // Multiple required actions for one resource:
 *   if (perms.can({ project: ["update", "assign_locations"] })) {
 *     // can edit AND assign locations
 *   }
 *
 *   // Building a spec separately:
 *   const needPublish = perms.capability("project", "publish");
 *   if (perms.can(needPublish)) {
 *     // publish ok
 *   }
 *
 * Migration Guidance:
 * - Prefer `can()` over role comparisons: (role === 'admin') → can("project","publish").
 * - Legacy booleans will be removed in a later phase; avoid adding new code paths using them.
 * - For composite UI enablement (e.g., needs any of several actions) write custom logic:
 *     const canAny = ["publish","approve"].some(a => perms.can("project", a));
 *
 * Performance Notes:
 * - The hook memoizes the effective map; recalculations occur only when role or org metadata changes.
 * - Permission checks (`can`) are O(r + a) for the spec size, typically tiny.
 *
 * Error Handling / Loading:
 * - While role/org are loading, role is undefined and `can()` returns false.
 * - Callers that need a loading state should pair with the underlying queries if necessary.
 */
export const usePermissions = () => {
  const slug = useOrganizationSlug();
  const orgQuery = useOrganizationBySlug(slug);
  const role = useOrganizationMemberRole();

  const effective = useMemo(
    () => buildEffectivePermissions(role, orgQuery.data?.metadata),
    [role, orgQuery.data?.metadata],
  );

  // Legacy flags (can be removed once UI migrates fully to can())
  const legacy = useMemo(
    () => legacyFlagsFromEffective(role, effective),
    [role, effective],
  );

  /**
   * Runtime permission predicate.
   * Overloads:
   *   can("project", "publish")
   *   can("project", "create", "update")  // all listed actions required
   *   can({ project: ["publish"], location: ["assign"] }) // multi-resource spec
   * If only (resource) is supplied it defaults to ["read"].
   * Returns false during initial load (optimistic hiding of UI elements).
   */
  function can(resource: string, ...actions: string[]): boolean;
  function can(spec: CapabilitySpec): boolean;
  function can(arg1: any, ...rest: string[]) {
    if (!arg1) return false;
    let spec: CapabilitySpec;
    if (typeof arg1 === "string") {
      spec = { [arg1]: rest.length ? rest : ["read"] };
    } else {
      spec = arg1;
    }
    return hasAllPermissions(effective, spec);
  }

  // Provide a convenience wrapper for building a spec: permissions.capability("project","publish")
  const build = capability;

  // Warn if consumers still rely on direct role checks (can be removed later)
  if (
    process.env.NODE_ENV !== "production" &&
    role &&
    (legacy as any)._warned !== true
  ) {
    // Attach a non-enumerable flag to avoid repeated warnings.
    Object.defineProperty(legacy, "_warned", {
      value: true,
      enumerable: false,
    });
  }

  return {
    role,
    effective,
    can,
    capability: build,
    // Expose full vocab via effective keys for debugging/export if needed
    ...legacy, // Backward compatibility booleans
  };
};

export type UsePermissionsReturn = ReturnType<typeof usePermissions>;
