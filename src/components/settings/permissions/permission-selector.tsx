"use client";

import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { BASE_ROLE_PERMISSIONS, getStatements } from "@/libs/auth-permissions";
import { useMemo } from "react";

export type PermissionMap = Record<string, string[]>;

interface PermissionSelectorProps {
  permissions: PermissionMap;
  onPermissionChange: (permissions: PermissionMap) => void;
  roleName: string;
  isBuiltInRole?: boolean;
  disabled?: boolean;
}

export function PermissionSelector({
  permissions,
  onPermissionChange,
  roleName,
  isBuiltInRole,
  disabled = false,
}: PermissionSelectorProps) {
  const statements = useMemo(() => getStatements(), []);

  const toggleAction = (resource: string, action: string, checked: boolean) => {
    const current = new Set(permissions[resource] || []);

    if (checked) {
      current.add(action);
    } else {
      current.delete(action);
    }

    const updatedPermissions = { ...permissions };
    if (current.size > 0) {
      updatedPermissions[resource] = Array.from(current);
    } else {
      delete updatedPermissions[resource];
    }

    onPermissionChange(updatedPermissions);
  };

  return (
    <div className="space-y-3">
      {Object.entries(statements).map(([resource, actions]) => (
        <div key={resource} className="space-y-2">
          <div className="text-sm font-medium">{resource}</div>
          <div className="flex flex-wrap gap-3">
            {(actions as string[]).map((a) => {
              const checked = new Set(permissions[resource] || []).has(a);
              const baseChecked =
                !!BASE_ROLE_PERMISSIONS[roleName]?.[resource]?.includes(a);
              const isDisabled = disabled || (isBuiltInRole && baseChecked);

              return (
                <label
                  key={`${resource}-${a}`}
                  className="flex items-center gap-2 text-sm"
                >
                  <Checkbox
                    checked={checked}
                    disabled={isDisabled}
                    onCheckedChange={(c) => toggleAction(resource, a, !!c)}
                  />
                  <span>
                    {a}
                    {baseChecked && (
                      <Badge
                        variant="outline"
                        className="ml-2 border-blue-200 bg-blue-50 text-xs text-blue-700"
                      >
                        Base
                      </Badge>
                    )}
                  </span>
                </label>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}
