"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import { useDialog } from "@/hooks/use-dialog";
import {
  useCreateOrgRoleMutation,
  useDeleteOrgRoleMutation,
  useOrgRoles,
  useUpdateOrgRoleMutation,
} from "@/queries/organization.queries";
import { RoleEditorDialog } from "./role-editor-dialog";

import { Roles } from "@/types/organization.types";
import { IconEdit, IconPlus, IconTrash } from "@tabler/icons-react";
import { useState } from "react";

interface Props {
  orgId?: string;
}

type PermissionMap = Record<string, string[]>;

const isBuiltIn = (name: string) =>
  name === Roles.ADMIN || name === Roles.MANAGER || name === Roles.MEMBER;

export function PermissionRoleManagement({ orgId }: Props) {
  const rolesQuery = useOrgRoles(orgId || "");

  const createRole = useCreateOrgRoleMutation(orgId || "");
  const updateRole = useUpdateOrgRoleMutation();
  const deleteRole = useDeleteOrgRoleMutation();

  const [openDeleteDialog, deleteDialogHandlers] = useDialog();
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null);

  const roles: Array<{
    roleName?: string;
    role?: string;
    permission?: PermissionMap;
  }> = rolesQuery.data ?? [];

  const [dialogOpen, dialogOpenHandlers] = useDialog();
  const [editing, setEditing] = useState<null | {
    name: string;
    permission: PermissionMap;
  }>(null);

  const toNonEmptyPermission = (
    p: PermissionMap,
  ): Record<string, [string, ...string[]]> =>
    Object.fromEntries(
      Object.entries(p).map(([k, v]) => [k, v as [string, ...string[]]]),
    ) as Record<string, [string, ...string[]]>;

  const openCreate = () => {
    setEditing(null);
    dialogOpenHandlers.open();
  };

  const openEdit = (name: string, permission: PermissionMap = {}) => {
    setEditing({ name, permission });
    dialogOpenHandlers.open();
  };

  const handleDialogSubmit = async (
    newName: string,
    newPerm: PermissionMap,
  ) => {
    if (!orgId) return;

    if (editing) {
      await updateRole.mutateAsync({
        orgId,
        roleName: editing.name,
        data: {
          ...(newName !== editing.name ? { roleName: newName } : {}),
          permission: toNonEmptyPermission(newPerm),
        },
      });
    } else {
      await createRole.mutateAsync({
        orgId,
        role: newName,
        permission: toNonEmptyPermission(newPerm),
      });
    }
    dialogOpenHandlers.close();
    setEditing(null);
  };

  const onRequestDelete = (roleName: string) => {
    if (isBuiltIn(roleName)) return;
    setRoleToDelete(roleName);
    deleteDialogHandlers.open();
  };

  const onConfirmDelete = async () => {
    if (!orgId || !roleToDelete) return;
    await deleteRole.mutateAsync({ orgId, roleName: roleToDelete });
    setRoleToDelete(null);
    deleteDialogHandlers.close();
  };

  return (
    <Card>
      <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
        <p className="text-base leading-6 font-medium">Roles</p>
        <Button
          size="sm"
          leftIcon={<IconPlus className="h-4 w-4" />}
          onClick={openCreate}
          disabled={!orgId}
        >
          New Role
        </Button>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {(roles || []).map((r: any) => {
            const roleName = r.roleName ?? r.role ?? "";
            const permission: PermissionMap = r.permission ?? {};
            const isCore = isBuiltIn(roleName);
            return (
              <Card key={roleName} className="p-4">
                <div className="flex items-start justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{roleName}</span>
                      {isCore && (
                        <Badge
                          variant="outline"
                          className="border-blue-200 bg-blue-50 text-xs text-blue-700"
                        >
                          Built-in
                        </Badge>
                      )}
                    </div>
                    <div className="mt-2 flex flex-wrap gap-2 text-sm">
                      {Object.entries(permission).length === 0 && (
                        <span className="text-gray-400">
                          No additive permissions
                        </span>
                      )}
                      {Object.entries(permission).flatMap(([res, acts]) =>
                        (acts as string[]).map((a) => (
                          <Badge
                            key={`${roleName}-${res}-${a}`}
                            variant="secondary"
                          >
                            {res}:{a}
                          </Badge>
                        )),
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEdit(roleName, permission)}
                      leftIcon={<IconEdit className="h-4 w-4" />}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={isCore}
                      onClick={() => onRequestDelete(roleName)}
                      leftIcon={<IconTrash className="h-4 w-4" />}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      <RoleEditorDialog
        open={dialogOpen}
        onOpenChange={(open) => {
          if (open) {
            dialogOpenHandlers.open();
          } else {
            dialogOpenHandlers.close();
            setEditing(null);
          }
        }}
        initialName={editing?.name}
        initialPermission={editing?.permission}
        isBuiltInRole={editing ? isBuiltIn(editing.name) : false}
        onSubmit={handleDialogSubmit}
      />

      <DeleteDialog
        title="Role"
        open={openDeleteDialog}
        onClose={() => {
          deleteDialogHandlers.close();
          setRoleToDelete(null);
        }}
        onDelete={onConfirmDelete}
        loading={deleteRole.isPending}
      />
    </Card>
  );
}
