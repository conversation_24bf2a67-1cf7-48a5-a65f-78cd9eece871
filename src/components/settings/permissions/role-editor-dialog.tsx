"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { BASE_ROLE_PERMISSIONS, getStatements } from "@/libs/auth-permissions";
import { Roles } from "@/types/organization.types";
import { useEffect, useMemo, useState } from "react";

export type PermissionMap = Record<string, string[]>;

const isBuiltIn = (name: string) =>
  name === Roles.ADMIN || name === Roles.MANAGER || name === Roles.MEMBER;

interface RoleEditorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialName?: string;
  initialPermission?: PermissionMap;
  isBuiltInRole?: boolean;
  onSubmit: (name: string, permission: PermissionMap) => Promise<void> | void;
}

export function RoleEditorDialog(props: RoleEditorDialogProps) {
  const {
    open,
    onOpenChange,
    initialName = "",
    initialPermission = {},
    isBuiltInRole,
    onSubmit,
  } = props;

  const [name, setName] = useState(initialName);
  const [perm, setPerm] = useState<PermissionMap>(initialPermission);

  useEffect(() => {
    if (open) {
      setName(initialName);
      setPerm(initialPermission || {});
    }
  }, [open, initialName, initialPermission]);

  const statements = useMemo(() => getStatements(), []);

  const toggleAction = (resource: string, action: string, checked: boolean) => {
    setPerm((prev) => {
      const current = new Set(prev[resource] || []);
      if (checked) current.add(action);
      else current.delete(action);
      const next = { ...prev } as PermissionMap;
      next[resource] = Array.from(current);
      if (!next[resource].length) delete next[resource];
      return next;
    });
  };

  const handleSubmit = async () => {
    if (!name.trim()) return;
    await onSubmit(name, perm);
    // Don't call onOpenChange here - let the parent handle closing
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{initialName ? "Edit Role" : "Create Role"}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label>Role name</Label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g. publisher"
              disabled={
                !!initialName && (isBuiltInRole || isBuiltIn(initialName))
              }
            />
          </div>

          <Separator />

          <div className="space-y-3">
            {Object.entries(statements).map(([resource, actions]) => (
              <div key={resource} className="space-y-2">
                <div className="text-sm font-medium">{resource}</div>
                <div className="flex flex-wrap gap-3">
                  {(actions as string[]).map((a) => {
                    const checked = new Set(perm[resource] || []).has(a);
                    const baseChecked =
                      !!BASE_ROLE_PERMISSIONS[name]?.[resource]?.includes(a);
                    const disabled =
                      !!initialName &&
                      (isBuiltInRole || isBuiltIn(initialName)) &&
                      baseChecked;
                    return (
                      <label
                        key={`${resource}-${a}`}
                        className="flex items-center gap-2 text-sm"
                      >
                        <Checkbox
                          checked={checked}
                          disabled={disabled}
                          onCheckedChange={(c) =>
                            toggleAction(resource, a, !!c)
                          }
                        />
                        <span>
                          {a}
                          {baseChecked && (
                            <Badge
                              variant="outline"
                              className="ml-2 border-blue-200 bg-blue-50 text-xs text-blue-700"
                            >
                              Base
                            </Badge>
                          )}
                        </span>
                      </label>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {initialName ? "Save" : "Create"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
