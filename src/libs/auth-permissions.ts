// Central permission vocabulary & base role capability definitions (Phase 1)
// This module is intentionally framework-agnostic and contains no React code.

import { Roles } from "@/types/organization.types";
import { createAccessControl } from "better-auth/plugins/access";
import { defaultStatements } from "better-auth/plugins/organization/access";

// Resource -> string[] actions map type
export type ResourceActions = Record<string, string[]>;

// Capability spec used when requesting authorization: { resource: [actions] }
export type CapabilitySpec = Record<string, string[]>;

// All canonical resources and their action vocabulary (initial scope)
// Keeping as an object for possible future metadata (descriptions, groups, etc.)
export const STATEMENTS = {
  project: [
    "create",
    "read",
    "update",
    "delete",
    "submit",
    "approve",
    "publish",
  ],
  approval: ["review"],
  member: ["invite", "update_role", "remove"],
  organization: ["read", "update"],
  location: ["read", "assign", "manage"],
} as const;

export type Resource = keyof typeof STATEMENTS;
export type Action = (typeof STATEMENTS)[keyof typeof STATEMENTS][number];

// Base role capability maps. Each role lists resources -> actions permitted.
// NOTE: 'publisher' is not a core role in the existing enum; we'll treat it as optional additive later.
// For now we only map existing roles to preserve backward compatibility.
// The 'publisher' virtual role is documented but not stored; support may be added later.
export const BASE_ROLE_PERMISSIONS: Record<string, ResourceActions> = {
  [Roles.MEMBER]: {
    project: ["create", "read", "update", "submit"],
  },
  [Roles.MANAGER]: {
    project: ["create", "read", "update", "submit"],
    approval: ["review"],
  },
  [Roles.ADMIN]: Object.fromEntries(
    Object.entries(STATEMENTS).map(([resource, actions]) => [
      resource,
      [...actions],
    ]),
  ) as ResourceActions,
  // Virtual role definition to allow potential composition; not part of Roles enum
  publisher: {
    project: ["publish"],
  },
};

// Shape for overrides stored in organization.metadata.permissions.overrides
// overrides: { roleName: { resource: [actions] } }
export interface PermissionOverrides {
  overrides?: Record<string, ResourceActions>;
}

// Shared sanitizer for overrides: drops unknown resources/actions and de-dupes.
export const sanitizeOverrides = (
  input: Record<string, unknown> | undefined | null,
): ResourceActions => {
  if (!input || typeof input !== "object") return {};
  const sanitized: ResourceActions = {};
  for (const [resource, value] of Object.entries(input)) {
    if (!(resource in STATEMENTS)) continue;
    const actions = Array.isArray(value) ? value : [];
    const valid = actions.filter((a): a is string =>
      (STATEMENTS as any)[resource].includes(a),
    );
    if (valid.length) sanitized[resource] = Array.from(new Set(valid));
  }
  return sanitized;
};

// Extract overrides for a given role from the raw org metadata (unvalidated) structure.
export const getRoleOverrides = (
  role: string,
  orgMeta: any,
): ResourceActions => {
  const overrides: PermissionOverrides | undefined = orgMeta?.permissions;
  const roleOverrides = overrides?.overrides?.[role];
  return sanitizeOverrides(roleOverrides as any);
};

// Base actions for a custom (tenant-defined) role from org metadata.
export const getCustomRoleBase = (
  role: string,
  orgMeta: any,
): ResourceActions => {
  const rolesDef = orgMeta?.permissions?.roles;
  const def = rolesDef?.[role];
  return sanitizeOverrides(def as any);
};

// Merge base + overrides (additive only) to compute effective permissions.
export const buildEffectivePermissions = (
  role: string | undefined | null,
  orgMetadata: any,
): ResourceActions => {
  if (!role) return {};
  const base =
    BASE_ROLE_PERMISSIONS[role] || getCustomRoleBase(role, orgMetadata);
  const overrides = getRoleOverrides(role, orgMetadata);
  const resources = new Set([...Object.keys(base), ...Object.keys(overrides)]);
  const effective: ResourceActions = {};
  for (const r of resources) {
    const union = new Set<string>([
      ...(base[r] || []),
      ...(overrides[r] || []),
    ]);
    if (union.size) effective[r] = Array.from(union);
  }
  return effective;
};

// Predicate to check if effective permissions satisfy a capability spec.
export const hasAllPermissions = (
  effective: ResourceActions,
  spec: CapabilitySpec,
): boolean => {
  return Object.entries(spec).every(([resource, requiredActions]) => {
    const allowed = new Set(effective[resource] || []);
    return requiredActions.every((a) => allowed.has(a));
  });
};

// Utility to build a capability spec easily: capability("project", "publish")
export const capability = (
  resource: Resource,
  ...actions: string[]
): CapabilitySpec => ({ [resource]: actions });

// Export a derived list of valid resource -> actions for UI/reference.
export const PERMISSION_VOCAB: Record<Resource, string[]> = Object.fromEntries(
  Object.entries(STATEMENTS).map(([k, v]) => [k, [...v]]),
) as any;

// Getter to provide a safe copy of the canonical statements (for UI and server usage)
export const getStatements = () =>
  Object.fromEntries(
    Object.entries(STATEMENTS).map(([k, v]) => [k, [...v]]),
  ) as Record<string, string[]>;

export type EffectivePermissions = ReturnType<typeof buildEffectivePermissions>;

// Phase 2: AC-centric alias for computing effective permissions
// For now this delegates to buildEffectivePermissions; in Phase 3 we may
// tighten sanitization and role resolution via AC accessors only.
export const evaluateEffectivePermissions = (
  role: string | undefined | null,
  orgMetadata: any,
) => buildEffectivePermissions(role, orgMetadata);

// Backward-compatible role based boolean helpers (used until Phase 3 refactor).
export const legacyFlagsFromEffective = (
  role: string | null | undefined,
  effective: EffectivePermissions,
) => {
  return {
    // Keep these aligned with existing UI expectations.
    canPublishProject: (effective.project || []).includes("publish"),
    canEditProject:
      role === Roles.ADMIN ||
      role === Roles.MANAGER ||
      (effective.project || []).includes("update"),
    canViewProject: !!effective.project,
    canManageMembers: (effective.member || []).some(
      (a) => a === "invite" || a === "update_role" || a === "remove",
    ),
    canViewReports: (effective.organization || []).includes("view_reports"),
    isAdmin: role === Roles.ADMIN,
    isManager: role === Roles.MANAGER,
    isMember: role === Roles.MEMBER,
  };
};

export default {
  STATEMENTS,
  BASE_ROLE_PERMISSIONS,
  buildEffectivePermissions,
  hasAllPermissions,
  capability,
  PERMISSION_VOCAB,
  legacyFlagsFromEffective,
  evaluateEffectivePermissions,
  sanitizeOverrides,
  getStatements,
};

/**
 * make sure to use `as const` so typescript can infer the type correctly
 */
export const ac = createAccessControl({
  ...defaultStatements,
  ...STATEMENTS,
});

export const admin = ac.newRole(
  Object.fromEntries(
    Object.entries(STATEMENTS).map(([resource, actions]) => [
      resource,
      [...actions],
    ]),
  ) as any,
);

export const manager = ac.newRole({
  project: ["create", "read", "update", "submit"],
  approval: ["review"],
});

export const member = ac.newRole({
  project: ["create", "read", "update", "submit"],
});
